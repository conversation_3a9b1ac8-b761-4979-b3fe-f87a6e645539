import { useState, useContext, useEffect } from 'react';
import { Team<PERSON>rovider, TeamContext } from "@/context/TeamContext";
import { ConfigContext } from "@/context/ConfigContext";
import { BusinessContext } from "@/context/BusinessContext";
import { <PERSON><PERSON>, DialogContent, DialogHeader, <PERSON>alogTitle, DialogFooter } from "@/components/ui/dialog";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Users } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { TabsContent } from "@/components/ui/tabs";
import { IWorkOrderRes, IBusinessTeamRes, ILocationTeamRes } from "@/types";
import { postToApi } from "@/lib/api";

/**
 * Tab that displays the team members assigned to a work order.
 *
 * The tab displays a table of all the team members assigned to the work order.
 * Each row in the table displays the team member's name, role, and a button
 * to remove the team member from the work order.
 *
 * If no team members are assigned to the work order, the tab displays a message
 * indicating that no team members have been assigned.
 *
 * The tab also displays a button to add new team members to the work order. When
 * the button is clicked, a dialog is opened that allows the user to select the
 * team members to add to the work order.
 *
 * @param workOrder The work order to display the team members for.
 * @returns {JSX element} for the tab.
 */
interface TeamMembersTabProps {
  workOrder: IWorkOrderRes
}
export function TeamMembersTab({ workOrder }: TeamMembersTabProps): JSX.Element {
  const { user } = useContext(ConfigContext) as { user: any };
  const { provider, setupUser } = useContext(BusinessContext) as { provider: any, setupUser: any };
  const {
    businessTeam,
    locationTeam,
    getBusinessTeam,
    getLocationTeam,
  } = useContext(TeamContext) as { //TODO: correct types
    businessTeam: IBusinessTeamRes[],
    locationTeam: ILocationTeamRes[],
    getBusinessTeam: any,
    getLocationTeam: any,
    addTeamMember: any,
    deleteTeamMember: any
  }

  const [isDialogOpen, setIsDialogOpen] = useState(false);

  useEffect(() => {
    setupUser(user);
    if (provider) {
      getBusinessTeam(user, provider, provider.locations[0]);
      getLocationTeam(user, provider.locations[0]);
    }
  }, [provider, user]);

  useEffect(() => {
    console.log("Business Team", businessTeam)
    console.log("Location Team", locationTeam)
  }, [businessTeam])

  const assignMember = (team: IBusinessTeamRes) => {
    const url = `/api/v1/work_order/${workOrder.id}/add_team_member`
    const data = {
      workOrderId: workOrder.id,
      userId: team.roleId,
      businessRoleId: team.id
    }
    console.log("url: ", url)
    console.log("data: ", data)

    postToApi(url, user, data,
      (response) => {
        console.log("result", response.result)
        getBusinessTeam(user, provider, provider.locations[0]);
        getLocationTeam(user, provider.locations[0]);
      }, (error) => {
        console.log("error", error)
      }
    )
  }

  const removeMember = (team: IBusinessTeamRes) => {
    const url = ``
    const data = {
      workOrderId: workOrder.id,
      userId: team.roleId,
      businessRoleId: team.id
    }
    // postToApi(url, user, data,
    //   (response) => {
    //     console.log("result", response.result)
    //     getBusinessTeam(user, provider, provider.locations[0]);
    //     getLocationTeam(user, provider.locations[0]);
    //   }, (error) => {
    //     console.log("error", error)
    //   }
    // )
  }

  return (
    <>
      <TabsContent value="team" className="space-y-4 mt-4">
        <Card>
          <CardContent className="pt-6">
            {workOrder.TeamMembers && workOrder.TeamMembers.length > 0 ? (
              <TeamMembersTable
                team={workOrder.TeamMembers}
                actions={[{ action: removeMember, label: "Remove" }]}
              />
            ) : (
              <div className="text-center py-6">
                <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">No Team Members Assigned</h3>
                <p className="text-muted-foreground">
                  No technicians have been assigned to this work order.
                </p>
                <Button
                  onClick={() => setIsDialogOpen(true)}
                  className="btn btn-primary mt-4">Assign Team Members
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      </TabsContent>
      {isDialogOpen &&
        <AddTeamMembersDialog
          setIsDialogOpen={setIsDialogOpen}
          businessTeam={businessTeam}
          assignMember={assignMember}
        />
      }
    </>
  )
}


/**
 * AddTeamMembersDialog displays a dialog that allows the user to assign technicians to a work order.
*
* @param {AddTeamMembersDialogProps} props
* @returns {JSX.Element}
*/
interface AddTeamMembersDialogProps {
  setIsDialogOpen: (value: boolean) => void,
  businessTeam: IBusinessTeamRes[],
  assignMember: (team: IBusinessTeamRes) => void
}
function AddTeamMembersDialog({ setIsDialogOpen, businessTeam, assignMember }: AddTeamMembersDialogProps): JSX.Element {
  return (
    <Dialog open={true} onOpenChange={setIsDialogOpen}>
      <DialogContent className="sm:max-w-[900px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Add Team Members</DialogTitle>
        </DialogHeader>
        {businessTeam && businessTeam.length > 0 ? (
          <TeamMembersTable
            team={businessTeam}
            actions={
              [{ action: assignMember, label: "Assign" }]
            } />
        ) : (
          (
            <div className="text-center py-6">
              <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">No Team Members Available</h3>
              <p className="text-muted-foreground">
                No technicians have been assigned to this work order.
              </p>
            </div>
          )
        )}
        <DialogFooter>
          <Button onClick={() => setIsDialogOpen(false)}>Close</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}



/**
 * Renders a table of team members with provided actions.
 * 
 * @param {TeamMembersTableProps} props - The properties for the component.
 * @returns {JSX.Element} The JSX code for rendering the team members table.
*/
interface Action {
  action: (member: ILocationTeamRes) => void;
  label: string;
}

interface TeamMembersTableProps {
  team: IBusinessTeamRes[] | ILocationTeamRes[];
  actions: Action[];
}
function TeamMembersTable({ team, actions }: TeamMembersTableProps): JSX.Element {
  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Photo</TableHead>
          <TableHead>Name</TableHead>
          <TableHead>Email</TableHead>
          <TableHead>Type</TableHead>
          <TableHead>Permissions</TableHead>
          <TableHead>Services</TableHead>
          <TableHead>Actions</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {team.map((member, index) => (
          <TableRow key={index}>
            <TableCell>
              <img
                src={member.Photo}
                alt="Profile Picture"
                className="w-10 h-10 rounded-full"
              />
            </TableCell>
            <TableCell>{member.Name || "N/A"}</TableCell>
            <TableCell>{member.Email || "N/A"}</TableCell>
            <TableCell>{member.type || "N/A"}</TableCell>
            <TableCell>{member.permissions?.split(',').join(', ') || "N/A"}</TableCell>
            <TableCell>{member.services || "N/A"}</TableCell>
            <TableCell>
              {actions.map((action, index) => (
                <Button
                  key={index}
                  onClick={() => action.action(member)}
                  className="btn btn-primary"
                >
                  {action.label}
                </Button>
              ))}
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
}
