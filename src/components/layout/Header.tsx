
import { useContext, useState } from "react";
import { useNavigate } from "react-router-dom";
import { ThemeToggle } from "@/components/ui/ThemeToggle";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Search, Menu, X, EyeOffIcon, EyeIcon } from "lucide-react";
import { cn } from "@/lib/utils";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter, DialogTrigger } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { postToApi } from "@/lib/api";
import { ConfigProvider, ConfigContext } from "@/context/ConfigContext";

interface HeaderProps {
  toggleSidebar: () => void;
  isSidebarOpen: boolean;
}

export function Header({ toggleSidebar, isSidebarOpen }: HeaderProps) {
  const [isSearchFocused, setIsSearchFocused] = useState(false);
  const { user, setUser } = useContext(ConfigContext);
  const navigate = useNavigate();

  console.log("user", user)

  return (
    <header className="border-b bg-background/95 backdrop-blur sticky top-0 z-40 w-full">
      <div className="container flex h-16 items-center justify-between py-4">
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="icon"
            id="sidebar-toggle"
            onClick={toggleSidebar}
            aria-label="Toggle sidebar"
            className="transition-transform duration-300"
          >
            {isSidebarOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
          </Button>
          <a href="/" className="flex items-center space-x-2">
            <img src="/logo.png" alt="OpenZcan" className="h-8 w-8" />
            <span className="font-bold text-xl">OpenZcan</span>
          </a>
        </div>

        <div className="hidden md:flex items-center justify-center flex-1 px-4">
          <div className={cn(
            "relative w-full max-w-md transition-all duration-300",
            isSearchFocused ? "scale-105" : ""
          )}>
            <Search className="absolute left-2 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              placeholder="Search..."
              className="pl-8 bg-secondary"
              onFocus={() => setIsSearchFocused(true)}
              onBlur={() => setIsSearchFocused(false)}
            />
          </div>
        </div>

        <div className="flex items-center gap-4">
          {user == null && <div className="hidden sm:flex gap-2">
            <Dialog>
              <DialogTrigger asChild>
                <Button variant="ghost">Log in</Button>
              </DialogTrigger>
              <LoginDialog kind="login" />
            </Dialog>

            <Dialog>
              <DialogTrigger asChild>
                <Button>Sign up</Button>
              </DialogTrigger>
              <LoginDialog kind="signup" />
            </Dialog>
          </div>}
          {user != null && <div className="hidden sm:flex gap-2">
            <Button 
              variant="ghost"
              onClick={() => navigate('/account')}
            >
              {user.email}
            </Button>
            <Button variant="ghost"
              onClick={(e) => {
                e.preventDefault();
                // Add login logic here
                console.log("Logout submitted");
                const url = "/api/v1/user/logout"
                postToApi(url, { id: 0 }, {}, (response) => {
                  console.log("result", response.result)
                  setUser(null)
                }, (error) => {
                  console.log("error", error)
                })
              }}
            >Logout</Button>
          </div>}
          <ThemeToggle />
        </div>
      </div>
    </header>
  );
}

/*
Displays a different dialog for login and sign up depending on the mode 
*/
const LoginDialog = ({ kind }) => {

  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [mode, setMode] = useState(kind);
  const [showPassword, setShowPassword] = useState(false);

  const { user, setUser } = useContext(ConfigContext);

  const titleForMode = () => {
    switch (mode) {
      case "login":
        return "Login";
      case "signup":
        return "Sign Up";
      case "forgot":
        return "Reset Password";
      default:
        return "Login";
    }
  }
  const onEmailChange = (e) => {
    setEmail(e.target.value);
  }
  const onPasswordChange = (e) => {
    setPassword(e.target.value);
  }

  const postToUrl = (url) => {

    let map = {
      email: email,
      password: password
    }
    postToApi(url, { id: 0 }, map, (response) => {
      console.log("result", response.result)
      setUser(response.result)
    }, (error) => {
      console.log("error", error)
    })
  }

  const onSubmit = () => {
    // handle form submission
    switch (mode) {
      case "login":
        // handle login
        // post a request to the server update the user in config context
        postToUrl("/api/v1/user/login")
        break;
      case "signup":
        // handle sign up
        // post a request to the server update the user in config context
        postToUrl("/api/v1/user/signup")
        break;
      case "forgot":
        // handle forgot password
        postToUrl("/api/v1/user/forgot")
        break;
    }
  }

  return (<DialogContent className="sm:max-w-[425px]">
    <DialogHeader>
      <DialogTitle>{titleForMode()}</DialogTitle>
      <DialogDescription>
        Enter your credentials to {mode == "signup" ? "create" : "access"} your account.
      </DialogDescription>
    </DialogHeader>
    <div className="grid gap-4 py-4">
      <div className="grid gap-2">
        <Label htmlFor="email">Email</Label>
        <Input id="email" type="email" placeholder="Enter your email" onChange={onEmailChange} />
      </div>
      {mode != "forgot" && <div className="grid gap-2">
        <Label htmlFor="password">Password</Label>
        <div className="relative">
          <Input id="password" type={showPassword ? "text" : "password"} placeholder="Enter your password" onChange={onPasswordChange} />
          <button
            type="button"
            className="absolute right-2 top-1/2 -translate-y-1/2"
            onClick={() => setShowPassword(!showPassword)}
          >
            {showPassword ? <EyeOffIcon /> : <EyeIcon />}
          </button>
        </div>
      </div>}
    </div>
    <DialogFooter>
      <Button
        type="submit"
        className="w-full"
        onClick={(e) => {
          e.preventDefault();
          // Add login logic here
          console.log("Login submitted");
          onSubmit();
        }}
      >
        Submit
      </Button>
      {mode != "forgot" && <Button
        variant="link"
        className="text-sm text-muted-foreground hover:text-primary"
        onClick={(e) => {
          e.preventDefault();
          // Add forgot password logic here
          console.log("Forgot password clicked");
          setMode("forgot");
        }}
      >
        Forgot password?
      </Button>}
    </DialogFooter>
    <div className="mt-4 text-center text-sm text-muted-foreground">
      Don't have an account?{" "}
      <Button
        variant="link"
        className="p-0 text-primary hover:text-primary/90"
        onClick={() => {
          // Add sign up logic here
          console.log("Sign up clicked");
          setMode("signup");
        }}
      >
        Sign up
      </Button>
    </div>
  </DialogContent>)
}
